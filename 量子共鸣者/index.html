<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量子共鸣者 - Quantum Resonance</title>
    <meta name="description" content="捕捉决定性瞬间，引燃无限可能。通过操控量子粒子的共鸣频率，在多维空间中创造连锁反应。">
    <meta name="keywords" content="量子共鸣者,音乐游戏,节奏游戏,物理模拟,策略游戏">
    <meta name="author" content="Quantum Resonance Team">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="styles/main.css" as="style">
    <link rel="preload" href="js/main.js" as="script">
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/game.css">
    <link rel="stylesheet" href="styles/ui-components.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <!-- PWA支持 -->
    <link rel="manifest" href="manifest.json">
    <meta name="theme-color" content="#1a1a2e">
    
    <!-- 图标 -->
    <link rel="icon" type="image/png" sizes="32x32" href="assets/images/favicon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="assets/images/favicon-16x16.png">
    <link rel="apple-touch-icon" href="assets/images/apple-touch-icon.png">
</head>
<body>
    <!-- 加载屏幕 -->
    <div id="loading-screen" class="screen active">
        <div class="loading-container">
            <div class="quantum-logo">
                <div class="particle-orbit">
                    <div class="particle"></div>
                    <div class="particle"></div>
                    <div class="particle"></div>
                </div>
                <div class="resonance-wave"></div>
            </div>
            <h1 class="game-title">量子共鸣者</h1>
            <p class="game-subtitle">Quantum Resonance</p>
            <div class="loading-progress">
                <div class="progress-bar">
                    <div class="progress-fill" id="loading-progress-fill"></div>
                </div>
                <p class="loading-text" id="loading-text">正在初始化量子场...</p>
            </div>
        </div>
    </div>

    <!-- 音频启用提示 -->
    <div id="audio-enable-prompt" class="audio-enable-prompt" style="display: none;">
        <div class="prompt-content">
            <div class="audio-icon">🔊</div>
            <h2>启用音频体验</h2>
            <p>为了获得最佳的游戏体验，请点击下方按钮启用音频系统</p>
            <button id="enable-audio-btn" class="enable-audio-btn">启用音频</button>
            <p class="audio-note">* 这是浏览器的安全要求，需要用户交互才能播放音频</p>
        </div>
    </div>

    <!-- 主菜单屏幕 -->
    <div id="main-menu-screen" class="screen">
        <div class="menu-container">
            <header class="game-header">
                <h1 class="main-title">量子共鸣者</h1>
                <p class="main-subtitle">捕捉决定性瞬间，引燃无限可能</p>
            </header>
            
            <nav class="main-menu">
                <button class="menu-btn primary" id="start-game-btn">
                    <span class="btn-icon">🎮</span>
                    <span class="btn-text" data-i18n="menu.startGame">开始游戏</span>
                </button>
                <button class="menu-btn" id="level-editor-btn">
                    <span class="btn-icon">🛠️</span>
                    <span class="btn-text" data-i18n="menu.levelEditor">关卡编辑器</span>
                </button>
                <button class="menu-btn" id="custom-levels-btn">
                    <span class="btn-icon">🌐</span>
                    <span class="btn-text" data-i18n="menu.customLevels">自定义关卡</span>
                </button>
                <button class="menu-btn" id="achievements-btn">
                    <span class="btn-icon">🏅</span>
                    <span class="btn-text" data-i18n="menu.achievements">成就系统</span>
                </button>
                <button class="menu-btn" id="leaderboard-btn">
                    <span class="btn-icon">🏆</span>
                    <span class="btn-text" data-i18n="menu.leaderboard">排行榜</span>
                </button>
                <button class="menu-btn" id="settings-btn">
                    <span class="btn-icon">⚙️</span>
                    <span class="btn-text" data-i18n="menu.settings">设置</span>
                </button>
            </nav>
            
            <div class="menu-footer">
                <div class="player-info">
                    <span class="player-name" id="current-player-name">访客</span>
                    <button class="switch-player-btn" id="switch-player-btn" data-i18n="menu.switchPlayer">切换玩家</button>
                </div>
                <div class="language-selector">
                    <button class="lang-btn" id="lang-btn">
                        <span class="lang-icon">🌍</span>
                        <span class="lang-text" id="current-lang">中文</span>
                    </button>
                </div>
            </div>
        </div>
        
        <!-- 背景粒子效果 -->
        <canvas id="menu-background-canvas" class="background-canvas"></canvas>
    </div>

    <!-- 关卡选择屏幕 -->
    <div id="levelSelectScreen" class="screen level-select-screen">
        <!-- 关卡选择内容将由JavaScript动态生成 -->
    </div>

    <!-- 关卡编辑器屏幕 -->
    <div id="levelEditorScreen" class="screen level-editor-screen">
        <!-- 关卡编辑器内容将由JavaScript动态生成 -->
    </div>

    <!-- 游戏结束屏幕 -->
    <div id="gameOverScreen" class="screen game-over-screen">
        <!-- 游戏结束内容将由JavaScript动态生成 -->
    </div>

    <!-- 成就屏幕 -->
    <div id="achievementsScreen" class="screen achievements-screen">
        <!-- 成就内容将由JavaScript动态生成 -->
    </div>

    <!-- 排行榜屏幕 -->
    <div id="leaderboardScreen" class="screen leaderboard-screen">
        <!-- 排行榜内容将由JavaScript动态生成 -->
    </div>

    <!-- 游戏屏幕 -->
    <div id="game-screen" class="screen">
        <!-- 游戏HUD -->
        <div class="game-hud">
            <div class="hud-top">
                <div class="level-info">
                    <span class="level-label" data-i18n="game.level">关卡</span>
                    <span class="level-number" id="current-level">1</span>
                </div>
                <div class="score-info">
                    <span class="score-label" data-i18n="game.score">得分</span>
                    <span class="score-value" id="current-score">0</span>
                </div>
                <div class="combo-info">
                    <span class="combo-label" data-i18n="game.combo">连击</span>
                    <span class="combo-value" id="current-combo">0</span>
                </div>
            </div>
            
            <div class="hud-controls">
                <button class="control-btn" id="pause-btn" title="暂停游戏">
                    <span class="btn-icon">⏸️</span>
                </button>
                <button class="control-btn" id="menu-btn" title="返回菜单">
                    <span class="btn-icon">🏠</span>
                </button>
            </div>
        </div>

        <!-- 主游戏画布 -->
        <div class="game-canvas-container">
            <canvas id="game-canvas" class="game-canvas"></canvas>
            <canvas id="ui-canvas" class="ui-canvas"></canvas>
            <canvas id="audioVisualizerCanvas" class="audio-visualizer-canvas"></canvas>
        </div>

        <!-- 频率控制面板 -->
        <div class="frequency-panel">
            <div class="frequency-controls">
                <div class="frequency-slider-container">
                    <label class="frequency-label" data-i18n="game.frequency">频率</label>
                    <input type="range" id="frequency-slider" class="frequency-slider" 
                           min="20" max="20000" value="440" step="1">
                    <span class="frequency-value" id="frequency-value">440 Hz</span>
                </div>
                <div class="resonance-indicator">
                    <div class="resonance-meter" id="resonance-meter">
                        <div class="resonance-fill" id="resonance-fill"></div>
                    </div>
                    <span class="resonance-label" data-i18n="game.resonance">共鸣强度</span>
                </div>
            </div>
            
            <!-- 音频可视化 -->
            <div class="audio-visualizer">
                <canvas id="visualizer-canvas" class="visualizer-canvas"></canvas>
            </div>
        </div>
    </div>

    <!-- 暂停屏幕 -->
    <div id="pause-screen" class="screen overlay">
        <div class="pause-container">
            <h2 class="pause-title" data-i18n="pause.title">游戏暂停</h2>
            <div class="pause-menu">
                <button class="pause-btn primary" id="resume-btn" data-i18n="pause.resume">继续游戏</button>
                <button class="pause-btn" id="restart-btn" data-i18n="pause.restart">重新开始</button>
                <button class="pause-btn" id="pause-settings-btn" data-i18n="pause.settings">设置</button>
                <button class="pause-btn" id="quit-btn" data-i18n="pause.quit">退出游戏</button>
            </div>
        </div>
    </div>

    <!-- 设置屏幕 -->
    <div id="settings-screen" class="screen overlay">
        <div class="settings-container">
            <h2 class="settings-title" data-i18n="settings.title">游戏设置</h2>
            <div class="settings-content">
                <!-- 音频设置 -->
                <div class="settings-section">
                    <h3 class="section-title" data-i18n="settings.audio">音频设置</h3>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.masterVolume">主音量</label>
                        <input type="range" id="master-volume" class="setting-slider" min="0" max="100" value="80">
                        <span class="setting-value" id="master-volume-value">80%</span>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.musicVolume">音乐音量</label>
                        <input type="range" id="music-volume" class="setting-slider" min="0" max="100" value="70">
                        <span class="setting-value" id="music-volume-value">70%</span>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.sfxVolume">音效音量</label>
                        <input type="range" id="sfx-volume" class="setting-slider" min="0" max="100" value="90">
                        <span class="setting-value" id="sfx-volume-value">90%</span>
                    </div>
                </div>

                <!-- 图形设置 -->
                <div class="settings-section">
                    <h3 class="section-title" data-i18n="settings.graphics">图形设置</h3>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.particleQuality">粒子质量</label>
                        <select id="particle-quality" class="setting-select">
                            <option value="low" data-i18n="settings.low">低</option>
                            <option value="medium" data-i18n="settings.medium" selected>中</option>
                            <option value="high" data-i18n="settings.high">高</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.visualEffects">视觉效果</label>
                        <input type="checkbox" id="visual-effects" class="setting-checkbox" checked>
                    </div>
                </div>

                <!-- 控制设置 -->
                <div class="settings-section">
                    <h3 class="section-title" data-i18n="settings.controls">控制设置</h3>
                    <div class="setting-item">
                        <label class="setting-label" data-i18n="settings.sensitivity">灵敏度</label>
                        <input type="range" id="sensitivity" class="setting-slider" min="0.1" max="2.0" value="1.0" step="0.1">
                        <span class="setting-value" id="sensitivity-value">1.0x</span>
                    </div>
                </div>
            </div>
            
            <div class="settings-footer">
                <button class="settings-btn secondary" id="reset-settings-btn" data-i18n="settings.reset">重置设置</button>
                <button class="settings-btn primary" id="save-settings-btn" data-i18n="settings.save">保存设置</button>
            </div>
        </div>
    </div>

    <!-- 玩家选择屏幕 -->
    <div id="player-screen" class="screen overlay">
        <div class="player-container">
            <h2 class="player-title" data-i18n="player.title">选择玩家</h2>
            <div class="player-list" id="player-list">
                <!-- 玩家列表将通过JavaScript动态生成 -->
            </div>
            <div class="player-actions">
                <button class="player-btn primary" id="create-player-btn" data-i18n="player.create">创建新玩家</button>
                <button class="player-btn secondary" id="close-player-btn" data-i18n="player.close">关闭</button>
            </div>
        </div>
    </div>

    <!-- 通用模态框 -->
    <div id="modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title" id="modal-title"></h3>
                <button class="modal-close" id="modal-close">&times;</button>
            </div>
            <div class="modal-body" id="modal-body"></div>
            <div class="modal-footer" id="modal-footer"></div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/math-utils.js"></script>
    <script src="js/core/audio-engine.js"></script>
    <script src="js/core/physics-engine.js"></script>
    <script src="js/core/quantum-engine.js"></script>
    <script src="js/audio/synthesizer.js"></script>
    <script src="js/audio/sequencer.js"></script>
    <script src="js/audio/effects.js"></script>
    <script src="js/audio/visualizer.js"></script>
    <script src="js/audio/audio-manager.js"></script>
    <script src="js/render/webgl-renderer.js"></script>
    <script src="js/render/particle-system-3d.js"></script>
    <script src="js/render/scene-manager-3d.js"></script>
    <script src="js/core/render-engine.js"></script>
    <script src="js/render/renderer.js"></script>
    <script src="js/render/particle-system.js"></script>
    <script src="js/ui/notification-system.js"></script>
    <script src="js/ui/ui-manager.js"></script>
    <script src="js/ui/ui-animations.js"></script>
    <script src="js/ui/game-hud.js"></script>
    <script src="js/ui/settings-panel.js"></script>
    <script src="js/ui/level-select.js"></script>
    <script src="js/ui/level-editor.js"></script>
    <script src="js/ui/game-over.js"></script>
    <script src="js/ui/achievements.js"></script>
    <script src="js/ui/leaderboard.js"></script>
    <script src="js/game/player-manager.js"></script>
    <script src="js/game/game-state.js"></script>
    <script src="js/game/level-manager.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/ui-components.js"></script>
    <script src="js/main.js"></script>

    <!-- 系统测试脚本（开发环境） -->
    <script src="test-systems.js"></script>
    <script src="performance-test.js"></script>
    <script src="compatibility-test.js"></script>
    <script src="game-functionality-test.js"></script>
    <script src="test-summary.js"></script>

    <!-- 修复验证脚本 -->
    <script src="fix-verification.js"></script>
    <script src="final-verification.js"></script>
    <script src="pc-layout-verification.js"></script>
    <script src="level-editor-verification.js"></script>
    <script src="screen-fix-verification.js"></script>
</body>
</html>
