/**
 * 量子共鸣者 - 主应用程序
 * 应用程序入口点，负责初始化和协调各个模块
 */

class QuantumResonanceApp {
    constructor() {
        this.isInitialized = false;
        this.isLoading = true;
        this.loadingProgress = 0;
        
        // 应用状态
        this.appState = 'loading'; // 'loading', 'menu', 'game', 'settings'
        
        // 版本信息
        this.version = '1.0.0';
        this.buildDate = '2024-07-30';
        
        console.log('🚀 量子共鸣者应用程序启动');
        console.log(`📦 版本: ${this.version} (${this.buildDate})`);
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('🚀 开始初始化应用程序...');
            
            // 显示加载界面
            this.showLoadingScreen();
            
            // 初始化步骤
            await this.initializeModules();
            await this.setupEventListeners();
            await this.loadDefaultLevels();
            await this.setupUI();
            
            // 完成初始化
            this.isInitialized = true;
            this.isLoading = false;
            this.appState = 'menu';
            
            // 隐藏加载界面，显示主菜单
            this.hideLoadingScreen();
            this.showMainMenu();
            
            console.log('✅ 应用程序初始化完成');
            
        } catch (error) {
            console.error('❌ 应用程序初始化失败:', error);
            this.showError('应用程序初始化失败', error.message);
        }
    }

    /**
     * 初始化各个模块
     */
    async initializeModules() {
        const steps = [
            { name: '存储服务', fn: () => storageService.init() },
            { name: '国际化服务', fn: () => i18n.init() },
            { name: 'UI管理器', fn: () => uiManager.init() },
            { name: 'UI动画系统', fn: () => uiAnimations.init() },
            { name: '关卡选择界面', fn: () => levelSelect.init() },
            { name: '关卡管理器', fn: () => levelManager.init() },
            { name: '关卡编辑器', fn: () => levelEditor.init() },
            { name: '游戏结束屏幕', fn: () => gameOver.init() },
            { name: '玩家系统管理器', fn: () => playerManager.init() },
            { name: '成就系统UI', fn: () => achievementsUI.init() },
            { name: '排行榜UI', fn: () => leaderboardUI.init() },
            { name: '渲染引擎', fn: () => this.initRenderEngine() },
            { name: '音频管理器', fn: () => audioManager.init() },
            { name: '游戏控制器', fn: () => gameController.init() },
        ];
        
        for (let i = 0; i < steps.length; i++) {
            const step = steps[i];
            console.log(`📦 初始化 ${step.name}...`);
            
            try {
                await step.fn();
                this.updateLoadingProgress((i + 1) / steps.length * 0.6); // 60% for modules
                console.log(`✅ ${step.name} 初始化完成`);
            } catch (error) {
                console.error(`❌ ${step.name} 初始化失败:`, error);
                throw new Error(`${step.name}初始化失败: ${error.message}`);
            }
        }
    }

    /**
     * 初始化渲染引擎
     */
    async initRenderEngine() {
        const canvas = document.getElementById('gameCanvas');
        if (!canvas) {
            throw new Error('游戏画布元素未找到');
        }

        // 初始化渲染引擎
        const success = await renderEngine.init(canvas);
        if (!success) {
            throw new Error('渲染引擎初始化失败');
        }

        // 设置音频可视化器画布
        this.setupAudioVisualizer();

        console.log(`🎨 渲染引擎初始化完成 (模式: ${renderEngine.getRenderMode()})`);
    }

    /**
     * 设置音频可视化器
     */
    setupAudioVisualizer() {
        const visualizerCanvas = document.getElementById('audioVisualizerCanvas');
        if (visualizerCanvas && audioManager && audioManager.visualizer) {
            audioManager.setupVisualizer(visualizerCanvas);
            console.log('🎵 音频可视化器已设置');
        }
    }

    /**
     * 设置事件监听器
     */
    async setupEventListeners() {
        console.log('🔗 设置事件监听器...');
        
        // 窗口事件
        window.addEventListener('beforeunload', () => this.onBeforeUnload());
        window.addEventListener('resize', () => this.onWindowResize());
        window.addEventListener('visibilitychange', () => this.onVisibilityChange());
        
        // 错误处理
        window.addEventListener('error', (e) => this.onError(e));
        window.addEventListener('unhandledrejection', (e) => this.onUnhandledRejection(e));
        
        // 菜单按钮事件
        this.setupMenuEvents();
        
        this.updateLoadingProgress(0.8); // 80%
    }

    /**
     * 设置菜单事件
     */
    setupMenuEvents() {
        // 开始游戏按钮
        const startButton = document.getElementById('startGameButton');
        if (startButton) {
            startButton.addEventListener('click', () => this.startGame());
        }
        
        // 设置按钮
        const settingsButton = document.getElementById('settingsButton');
        if (settingsButton) {
            settingsButton.addEventListener('click', () => this.showSettings());
        }
        
        // 关于按钮
        const aboutButton = document.getElementById('aboutButton');
        if (aboutButton) {
            aboutButton.addEventListener('click', () => this.showAbout());
        }
        
        // 返回主菜单按钮
        const backButtons = document.querySelectorAll('.back-to-menu');
        backButtons.forEach(button => {
            button.addEventListener('click', () => this.showMainMenu());
        });
        
        // 暂停菜单按钮
        const pauseButton = document.getElementById('pauseButton');
        if (pauseButton) {
            pauseButton.addEventListener('click', () => gameController.pauseGame());
        }
        
        // 恢复游戏按钮
        const resumeButton = document.getElementById('resumeButton');
        if (resumeButton) {
            resumeButton.addEventListener('click', () => gameController.resumeGame());
        }
        
        // 重启游戏按钮
        const restartButton = document.getElementById('restartButton');
        if (restartButton) {
            restartButton.addEventListener('click', () => gameController.restartGame());
        }
        
        // 音频初始化按钮（需要用户交互）
        const initAudioButton = document.getElementById('initAudioButton');
        if (initAudioButton) {
            initAudioButton.addEventListener('click', async () => {
                await this.initializeAudio();
                initAudioButton.style.display = 'none';
            });
        }
    }

    /**
     * 加载默认关卡
     */
    async loadDefaultLevels() {
        console.log('📋 加载默认关卡...');
        
        // 注册基础关卡包
        levelManager.registerLevelPack('basic', {
            name: '基础教程',
            description: '学习量子共鸣的基本机制',
            levels: [
                {
                    name: '第一步',
                    description: '点击粒子来激活它们',
                    particles: [
                        { x: 300, y: 200, frequency: 440, radius: 15 },
                        { x: 500, y: 200, frequency: 880, radius: 15 }
                    ],
                    targetScore: 500,
                    timeLimit: 30,
                    objectives: [
                        { type: 'activateParticles', target: 2, description: '激活所有粒子' }
                    ]
                },
                {
                    name: '共鸣链',
                    description: '创建粒子之间的共鸣连锁反应',
                    particles: [
                        { x: 200, y: 200, frequency: 440, radius: 12 },
                        { x: 350, y: 200, frequency: 440, radius: 12 },
                        { x: 500, y: 200, frequency: 440, radius: 12 },
                        { x: 275, y: 300, frequency: 660, radius: 12 },
                        { x: 425, y: 300, frequency: 660, radius: 12 }
                    ],
                    targetScore: 1000,
                    timeLimit: 45,
                    objectives: [
                        { type: 'chainReaction', target: 3, description: '创建3个粒子的连锁反应' }
                    ]
                },
                {
                    name: '频率调节',
                    description: '学习调节频率来匹配不同的粒子',
                    particles: [
                        { x: 200, y: 150, frequency: 220, radius: 10 },
                        { x: 400, y: 150, frequency: 440, radius: 10 },
                        { x: 600, y: 150, frequency: 880, radius: 10 },
                        { x: 300, y: 300, frequency: 330, radius: 10 },
                        { x: 500, y: 300, frequency: 660, radius: 10 }
                    ],
                    targetScore: 1500,
                    timeLimit: 60,
                    rules: {
                        frequencyRange: { min: 200, max: 1000 }
                    }
                }
            ]
        });
        
        // 注册进阶关卡包
        levelManager.registerLevelPack('advanced', {
            name: '进阶挑战',
            description: '更复杂的量子共鸣挑战',
            levels: [
                {
                    name: '量子迷宫',
                    description: '在复杂的粒子网络中找到最佳路径',
                    particles: this.generateMazeParticles(),
                    targetScore: 2500,
                    timeLimit: 90,
                    maxMoves: 20
                },
                {
                    name: '共鸣风暴',
                    description: '在动态变化的环境中保持共鸣',
                    particles: this.generateStormParticles(),
                    forceFields: [
                        { x: 400, y: 300, type: 'vortex', strength: 150, radius: 200 }
                    ],
                    targetScore: 3000,
                    timeLimit: 120,
                    rules: {
                        damping: 0.95,
                        fieldStrength: 1.5
                    }
                }
            ]
        });
        
        this.updateLoadingProgress(0.9); // 90%
    }

    /**
     * 生成迷宫粒子配置
     */
    generateMazeParticles() {
        const particles = [];
        const gridSize = 8;
        const cellSize = 80;
        const baseFreq = 220;
        
        for (let x = 0; x < gridSize; x++) {
            for (let y = 0; y < gridSize; y++) {
                // 跳过一些位置创建迷宫效果
                if ((x + y) % 3 === 0) continue;
                
                particles.push({
                    x: 100 + x * cellSize,
                    y: 100 + y * cellSize,
                    frequency: baseFreq + (x + y) * 30,
                    radius: 8,
                    isTarget: x === gridSize - 1 && y === gridSize - 1
                });
            }
        }
        
        return particles;
    }

    /**
     * 生成风暴粒子配置
     */
    generateStormParticles() {
        const particles = [];
        const centerX = 400;
        const centerY = 300;
        const radius = 200;
        const count = 12;
        
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2;
            const x = centerX + Math.cos(angle) * radius;
            const y = centerY + Math.sin(angle) * radius;
            
            particles.push({
                x: x,
                y: y,
                frequency: 440 + i * 55,
                radius: 10,
                energy: 1 + Math.random()
            });
        }
        
        return particles;
    }

    /**
     * 设置UI
     */
    async setupUI() {
        console.log('🎨 设置用户界面...');
        
        // 更新版本信息
        const versionElement = document.getElementById('versionInfo');
        if (versionElement) {
            versionElement.textContent = `v${this.version}`;
        }
        
        // 设置语言选择器
        const languageSelect = document.getElementById('languageSelect');
        if (languageSelect) {
            languageSelect.addEventListener('change', (e) => {
                i18n.setLanguage(e.target.value);
            });
        }
        
        // 初始化设置面板
        this.initializeSettingsPanel();

        // 设置音频可视化器
        this.setupAudioVisualizer();

        this.updateLoadingProgress(1.0); // 100%
    }

    /**
     * 设置音频可视化器
     */
    setupAudioVisualizer() {
        const visualizerCanvas = document.getElementById('audioVisualizerCanvas');
        if (visualizerCanvas && audioManager) {
            audioManager.setupVisualizer(visualizerCanvas);
            console.log('📊 音频可视化器已设置');
        }
    }

    /**
     * 初始化设置面板
     */
    initializeSettingsPanel() {
        // 音量控制
        const volumeSliders = {
            master: document.getElementById('masterVolumeSlider'),
            music: document.getElementById('musicVolumeSlider'),
            sfx: document.getElementById('sfxVolumeSlider')
        };
        
        Object.entries(volumeSliders).forEach(([type, slider]) => {
            if (slider) {
                slider.addEventListener('input', (e) => {
                    const volume = parseFloat(e.target.value);
                    if (audioEngine.isReady()) {
                        audioEngine.setVolume(type, volume);
                    }
                    gameController.settings[`${type}Volume`] = volume;
                    gameController.saveSettings();
                });
            }
        });
        
        // 图形设置
        const particleEffectsToggle = document.getElementById('particleEffectsToggle');
        if (particleEffectsToggle) {
            particleEffectsToggle.addEventListener('change', (e) => {
                gameController.settings.enableParticleEffects = e.target.checked;
                renderEngine.enableParticleTrails = e.target.checked;
                renderEngine.enableGlowEffects = e.target.checked;
                gameController.saveSettings();
            });
        }
        
        // 调试模式
        const debugToggle = document.getElementById('debugToggle');
        if (debugToggle) {
            debugToggle.addEventListener('change', (e) => {
                renderEngine.showDebugInfo = e.target.checked;
            });
        }
    }

    /**
     * 初始化音频（需要用户交互）
     */
    async initializeAudio() {
        try {
            console.log('🎵 初始化音频系统...');

            // 初始化音频引擎
            await audioEngine.init();
            console.log('✅ 音频引擎初始化完成');

            // 初始化音频管理器
            if (window.audioManager && !audioManager.isInitialized) {
                await audioManager.init();
                console.log('✅ 音频管理器初始化完成');
            }

            console.log('✅ 音频系统初始化完成');
        } catch (error) {
            console.error('❌ 音频系统初始化失败:', error);
        }
    }

    /**
     * 开始游戏
     */
    async startGame() {
        try {
            this.appState = 'game';

            // 确保音频已初始化
            if (!audioEngine.isReady()) {
                await this.initializeAudio();
            }

            // 确保音频管理器已初始化
            if (window.audioManager && !audioManager.isInitialized) {
                await audioManager.init();
            }

            // 开始第一个关卡
            await gameController.startGame();

        } catch (error) {
            console.error('❌ 游戏启动失败:', error);
            this.showError('游戏启动失败', error.message);
        }
    }

    /**
     * 显示主菜单
     */
    showMainMenu() {
        this.appState = 'menu';
        this.hideAllScreens();
        document.getElementById('mainMenu').style.display = 'block';
    }

    /**
     * 显示设置界面
     */
    showSettings() {
        this.appState = 'settings';
        this.hideAllScreens();
        document.getElementById('settingsScreen').style.display = 'block';
    }

    /**
     * 显示关于界面
     */
    showAbout() {
        this.hideAllScreens();
        document.getElementById('aboutScreen').style.display = 'block';
    }

    /**
     * 显示加载界面
     */
    showLoadingScreen() {
        document.getElementById('loadingScreen').style.display = 'flex';
    }

    /**
     * 隐藏加载界面
     */
    hideLoadingScreen() {
        document.getElementById('loadingScreen').style.display = 'none';
    }

    /**
     * 隐藏所有界面
     */
    hideAllScreens() {
        const screens = [
            'loadingScreen', 'mainMenu', 'gameScreen', 
            'settingsScreen', 'aboutScreen', 'pauseScreen'
        ];
        
        screens.forEach(screenId => {
            const screen = document.getElementById(screenId);
            if (screen) {
                screen.style.display = 'none';
            }
        });
    }

    /**
     * 更新加载进度
     * @param {number} progress - 进度值 (0-1)
     */
    updateLoadingProgress(progress) {
        this.loadingProgress = progress;
        
        const progressBar = document.getElementById('loadingProgress');
        if (progressBar) {
            progressBar.style.width = `${progress * 100}%`;
        }
        
        const progressText = document.getElementById('loadingText');
        if (progressText) {
            progressText.textContent = `${Math.round(progress * 100)}%`;
        }
    }

    /**
     * 显示错误信息
     * @param {string} title - 错误标题
     * @param {string} message - 错误消息
     */
    showError(title, message) {
        console.error(`❌ ${title}: ${message}`);
        
        // 简单的错误显示，后续可以改为更好的UI
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message';
        errorDiv.innerHTML = `
            <h3>${title}</h3>
            <p>${message}</p>
            <button onclick="this.parentElement.remove()">确定</button>
        `;
        
        document.body.appendChild(errorDiv);
    }

    // 事件处理器
    onBeforeUnload() {
        // 保存游戏状态
        if (gameController.isInitialized) {
            gameController.saveSettings();
        }
    }

    onWindowResize() {
        if (renderEngine.canvas) {
            renderEngine.resize();
        }
    }

    onVisibilityChange() {
        if (document.hidden && gameController.gameState === 'playing') {
            gameController.pauseGame();
        }
    }

    onError(event) {
        console.error('❌ 全局错误:', event.error);
        this.showError('应用程序错误', event.error.message);
    }

    onUnhandledRejection(event) {
        console.error('❌ 未处理的Promise拒绝:', event.reason);
        this.showError('异步操作失败', event.reason.message || event.reason);
    }
}

// 应用程序入口点
document.addEventListener('DOMContentLoaded', async () => {
    console.log('📄 DOM加载完成，启动应用程序...');
    
    // 创建应用程序实例
    window.app = new QuantumResonanceApp();
    
    // 初始化应用程序
    await app.init();
});

// 导出应用程序类
window.QuantumResonanceApp = QuantumResonanceApp;
